// Dart imports
import 'dart:async';
import 'dart:io';

// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

// Project imports
import 'package:culture_connect/models/message_model.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/providers/chat_provider.dart';
import 'package:culture_connect/providers/group_chat_provider.dart';
import 'package:culture_connect/providers/group_translation_provider.dart';
import 'package:culture_connect/providers/paginated_messages_provider.dart';
import 'package:culture_connect/screens/messaging/group_info_screen.dart';
import 'package:culture_connect/screens/messaging/group_translation_settings_screen.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';
import 'package:culture_connect/widgets/messaging/virtualized_message_list.dart';

/// A screen for displaying and interacting with a group chat
class GroupChatScreen extends ConsumerStatefulWidget {
  /// The ID of the group chat to display
  final String groupId;

  /// Creates a new group chat screen
  const GroupChatScreen({
    super.key,
    required this.groupId,
  });

  @override
  ConsumerState<GroupChatScreen> createState() => _GroupChatScreenState();
}

class _GroupChatScreenState extends ConsumerState<GroupChatScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ItemScrollController _scrollController = ItemScrollController();
  final ItemPositionsListener _positionsListener =
      ItemPositionsListener.create();
  bool _isAttachmentMenuOpen = false;
  bool _isRecording = false;

  @override
  void dispose() {
    _messageController.dispose();
    super.dispose();
  }

  void _loadMoreMessages() {
    final messagesNotifier =
        ref.read(paginatedMessagesProvider(widget.groupId).notifier);
    messagesNotifier.loadMoreMessages();
  }

  void _showMessageOptions(MessageModel message, bool isMe) {
    if (!mounted) return;

    showModalBottomSheet(
      context: context,
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.copy),
                title: const Text('Copy'),
                onTap: () {
                  // Copy message text to clipboard
                  Navigator.pop(context);
                },
              ),
              if (isMe)
                ListTile(
                  leading: const Icon(Icons.edit),
                  title: const Text('Edit'),
                  onTap: () {
                    // Edit message
                    Navigator.pop(context);
                  },
                ),
              ListTile(
                leading: const Icon(Icons.reply),
                title: const Text('Reply'),
                onTap: () {
                  // Reply to message
                  Navigator.pop(context);
                },
              ),
              ListTile(
                leading: const Icon(Icons.forward),
                title: const Text('Forward'),
                onTap: () {
                  // Forward message
                  Navigator.pop(context);
                },
              ),
              if (isMe)
                ListTile(
                  leading: const Icon(Icons.delete, color: Colors.red),
                  title:
                      const Text('Delete', style: TextStyle(color: Colors.red)),
                  onTap: () {
                    Navigator.pop(context);
                    _confirmDeleteMessage(message);
                  },
                ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _confirmDeleteMessage(MessageModel message) async {
    if (!mounted) return;

    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Delete Message'),
          content: const Text('Are you sure you want to delete this message?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              style: TextButton.styleFrom(
                foregroundColor: Colors.red,
              ),
              child: const Text('Delete'),
            ),
          ],
        );
      },
    );

    if (confirm == true && mounted) {
      try {
        // Get the messages notifier to remove the message locally
        final messagesNotifier =
            ref.read(paginatedMessagesProvider(widget.groupId).notifier);
        messagesNotifier.removeMessage(message.id);

        // Delete the message from Firestore using the chat provider
        // Since GroupChatNotifier doesn't have a deleteMessage method, we'll use the one from ChatProvider
        await ref.read(chatProvider.notifier).deleteMessage(message.id);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Message deleted')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error deleting message: $e')),
          );
        }
      }
    }
  }

  void _toggleAttachmentMenu() {
    setState(() {
      _isAttachmentMenuOpen = !_isAttachmentMenuOpen;
    });
  }

  void _toggleRecording() {
    setState(() {
      _isRecording = !_isRecording;
    });
  }

  void _sendMessage() {
    final text = _messageController.text.trim();
    if (text.isEmpty) return;

    try {
      // Create a new message
      final now = DateTime.now();
      final message = MessageModel(
        id: now.millisecondsSinceEpoch.toString(),
        chatId: widget.groupId,
        senderId: ref.read(authStateProvider).user!.id,
        thisecipientId: '',
        text: text,
        timestamp: now,
        status: MessageStatus.sent,
        type: MessageType.text,
        isGroupMessage: true,
      );

      // Send the message using the group chat provider
      ref.read(groupChatProvider.notifier).sendGroupMessage(message);

      _messageController.clear();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error sending message: $e')),
        );
      }
    }
  }

  Future<void> _pickImage() async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (pickedFile != null && mounted) {
        // Create a new message
        final now = DateTime.now();
        final message = MessageModel(
          id: now.millisecondsSinceEpoch.toString(),
          chatId: widget.groupId,
          senderId: ref.read(authStateProvider).user!.id,
          thisecipientId: '',
          text: '📷 Image',
          timestamp: now,
          status: MessageStatus.sending,
          type: MessageType.image,
          isGroupMessage: true,
        );

        // Process and send the image
        final file = File(pickedFile.path);
        await ref
            .read(groupChatProvider.notifier)
            .sendGroupMediaMessage(message, file);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error picking image: $e')),
        );
      }
    }
  }

  Future<void> _takePhoto() async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (pickedFile != null && mounted) {
        // Create a new message
        final now = DateTime.now();
        final message = MessageModel(
          id: now.millisecondsSinceEpoch.toString(),
          chatId: widget.groupId,
          senderId: ref.read(authStateProvider).user!.id,
          thisecipientId: '',
          text: '📷 Image',
          timestamp: now,
          status: MessageStatus.sending,
          type: MessageType.image,
          isGroupMessage: true,
        );

        // Process and send the image
        final file = File(pickedFile.path);
        await ref
            .read(groupChatProvider.notifier)
            .sendGroupMediaMessage(message, file);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error taking photo: $e')),
        );
      }
    }
  }

  Widget _buildAttachmentMenu() {
    return Container(
      padding: EdgeInsets.all(16),
      color: Colors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildAttachmentOption(
            icon: Icons.photo_library,
            label: 'Gallery',
            onTap: _pickImage,
          ),
          _buildAttachmentOption(
            icon: Icons.camera_alt,
            label: 'Camera',
            onTap: _takePhoto,
          ),
          _buildAttachmentOption(
            icon: Icons.location_on,
            label: 'Location',
            onTap: () {
              // Implement location sharing
            },
          ),
          _buildAttachmentOption(
            icon: Icons.contact_page,
            label: 'Contact',
            onTap: () {
              // Implement contact sharing
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAttachmentOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withAlpha(30),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: AppTheme.primaryColor,
              size: 24,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageInput() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13), // 0.05 * 255 ≈ 13
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          IconButton(
            icon: Icon(
              _isAttachmentMenuOpen ? Icons.close : Icons.attach_file,
              color: AppTheme.textSecondaryColor,
            ),
            onPressed: _toggleAttachmentMenu,
          ),
          Expanded(
            child: TextField(
              controller: _messageController,
              decoration: InputDecoration(
                hintText: 'Type a message...',
                hintStyle: const TextStyle(
                  color: AppTheme.textSecondaryColor,
                  fontSize: 16,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey[100],
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
              ),
              textInputAction: TextInputAction.send,
              onSubmitted: (_) => _sendMessage(),
            ),
          ),
          IconButton(
            icon: Icon(
              _isRecording ? Icons.stop : Icons.mic_none,
              color: _isRecording ? Colors.red : AppTheme.textSecondaryColor,
            ),
            onPressed: _toggleRecording,
          ),
          IconButton(
            icon: const Icon(
              Icons.send,
              color: AppTheme.primaryColor,
            ),
            onPressed: _sendMessage,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyChat() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          const Text(
            'No messages yet',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Start the conversation!',
            style: TextStyle(
              fontSize: 14,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final groupDetailsAsync =
        ref.watch(groupChatDetailsProvider(widget.groupId));

    return Scaffold(
      appBar: groupDetailsAsync.when(
        data: (group) {
          if (group == null) {
            return const CustomAppBar(
              title: 'Group Chat',
              showBackButton: true,
            );
          }

          return CustomAppBar(
            title: group.name,
            subTitle: '${group.members.length} members',
            showBackButton: true,
            actions: [
              IconButton(
                icon: const Icon(Icons.translate),
                onPressed: () {
                  if (mounted) {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => GroupTranslationSettingsScreen(
                          groupId: widget.groupId,
                        ),
                      ),
                    );
                  }
                },
                tooltip: 'Translation Settings',
              ),
              IconButton(
                icon: const Icon(Icons.info_outline),
                onPressed: () {
                  if (mounted) {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            GroupInfoScreen(groupId: widget.groupId),
                      ),
                    );
                  }
                },
              ),
            ],
          );
        },
        loading: () => const CustomAppBar(
          title: 'Loading...',
          showBackButton: true,
        ),
        error: (error, stack) => const CustomAppBar(
          title: 'Error',
          showBackButton: true,
        ),
      ),
      body: Column(
        children: [
          // Translation banner
          Consumer(
            builder: (context, ref, child) {
              final groupSettingsAsync =
                  ref.watch(groupTranslationSettingsProvider(widget.groupId));

              return groupSettingsAsync.when(
                data: (settings) {
                  if (!settings.enableRealTimeTranslation) {
                    return const SizedBox.shrink();
                  }

                  return Container(
                    padding: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                    color: AppTheme.primaryColor.withAlpha(30),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.translate,
                          size: 18,
                          color: AppTheme.primaryColor,
                        ),
                        const SizedBox(width: 8),
                        const Expanded(
                          child: Text(
                            'Real-time translation enabled',
                            style: TextStyle(
                              fontSize: 14,
                              color: AppTheme.primaryColor,
                            ),
                          ),
                        ),
                        TextButton(
                          onPressed: () {
                            if (mounted) {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) =>
                                      GroupTranslationSettingsScreen(
                                    groupId: widget.groupId,
                                  ),
                                ),
                              );
                            }
                          },
                          child: const Text(
                            'Settings',
                            style: TextStyle(
                              fontSize: 14,
                              color: AppTheme.primaryColor,
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
                loading: () => const SizedBox.shrink(),
                error: (_, __) => const SizedBox.shrink(),
              );
            },
          ),

          // Messages list
          Expanded(
            child: Consumer(
              builder: (context, ref, child) {
                // Watch the paginated messages
                // We don't need messagesState directly as we use sortedMessages
                final sortedMessages =
                    ref.watch(sortedMessagesProvider(widget.groupId));
                final isLoading =
                    ref.watch(messagesLoadingProvider(widget.groupId));
                final hasMore =
                    ref.watch(hasMoreMessagesProvider(widget.groupId));

                // Watch the current user
                final currentUserAsync = ref.watch(currentUserModelProvider);

                if (sortedMessages.isEmpty && !isLoading) {
                  return _buildEmptyChat();
                }

                return currentUserAsync.when(
                  data: (currentUser) {
                    if (currentUser == null) {
                      return const Center(child: Text('User not found'));
                    }

                    // Add a load more listener to the positions listener
                    _positionsListener.itemPositions.addListener(() {
                      final positions = _positionsListener.itemPositions.value;
                      if (positions.isEmpty) return;

                      // If we're near the top of the list and there are more messages to load
                      final firstVisible = positions.first.index;
                      if (firstVisible < 5 && hasMore && !isLoading) {
                        _loadMoreMessages();
                      }
                    });

                    return Stack(
                      children: [
                        VirtualizedMessageList(
                          groupId: widget.groupId,
                          messages: sortedMessages,
                          scrollController: _scrollController,
                          positionsListener: _positionsListener,
                          onMessageLongPress: (message) => _showMessageOptions(
                            message,
                            message.senderId == currentUser.id,
                          ),
                        ),

                        // Loading indicator at the top when loading more messages
                        if (isLoading)
                          Positioned(
                            top: 0,
                            left: 0,
                            right: 0,
                            child: Container(
                              padding: EdgeInsets.all(8),
                              color: Colors.black.withAlpha(25),
                              child: const Center(
                                child: CircularProgressIndicator(),
                              ),
                            ),
                          ),
                      ],
                    );
                  },
                  loading: () =>
                      const Center(child: CircularProgressIndicator()),
                  error: (error, stack) => Center(
                    child: Text('Error loading user: $error'),
                  ),
                );
              },
            ),
          ),

          // Attachment menu
          if (_isAttachmentMenuOpen) _buildAttachmentMenu(),

          // Message input
          _buildMessageInput(),
        ],
      ),
    );
  }
}
