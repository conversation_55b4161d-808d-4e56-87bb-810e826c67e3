import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:culture_connect/models/message_model.dart';
import 'package:culture_connect/models/user_model.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/providers/chat_provider.dart';
import 'package:culture_connect/screens/messaging/chat_screen.dart';
import 'package:culture_connect/screens/messaging/new_chat_screen.dart';
import 'package:culture_connect/screens/messaging/create_group_chat_screen.dart';
import 'package:culture_connect/screens/messaging/group_chat_screen.dart';
import 'package:culture_connect/models/group_chat_model.dart';
import 'package:culture_connect/providers/group_chat_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';
import 'package:culture_connect/widgets/skeleton_loading.dart';

class ChatListScreen extends ConsumerStatefulWidget {
  const ChatListScreen({super.key});

  @override
  ConsumerState<ChatListScreen> createState() => _ChatListScreenState();
}

class _ChatListScreenState extends ConsumerState<ChatListScreen>
    with SingleTickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;
  String _searchQuery = '';
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  void _startSearch() {
    setState(() {
      _isSearching = true;
    });
  }

  void _stopSearch() {
    setState(() {
      _isSearching = false;
      _searchQuery = '';
      _searchController.clear();
    });
  }

  void _updateSearchQuery(String query) {
    setState(() {
      _searchQuery = query.toLowerCase();
    });
  }

  void _navigateToChat(
      BuildContext context, String chatId, UserModel recipient) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChatScreen(
          chatId: chatId,
          recipient: recipient,
        ),
      ),
    );
  }

  void _navigateToGroupChat(BuildContext context, String groupId) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => GroupChatScreen(
          groupId: groupId,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final directChatsAsync = ref.watch(userChatsProvider);
    final groupChatsAsync = ref.watch(groupChatProvider);
    final currentUserAsync = ref.watch(currentUserModelProvider);

    return Scaffold(
      appBar: _buildAppBar(),
      body: TabBarView(
        controller: _tabController,
        children: [
          // Direct Messages Tab
          directChatsAsync.when(
            data: (chats) {
              if (chats.isEmpty) {
                return _buildEmptyState();
              }

              return currentUserAsync.when(
                data: (currentUser) {
                  if (currentUser == null) {
                    return const Center(child: Text('User not found'));
                  }

                  return _buildChatList(chats);
                },
                loading: () => _buildLoadingState(),
                error: (error, stack) => Center(
                  child: Text('Error loading user: $error'),
                ),
              );
            },
            loading: () => _buildLoadingState(),
            error: (error, stack) => Center(
              child: Text('Error loading chats: $error'),
            ),
          ),

          // Group Chats Tab
          groupChatsAsync.when(
            data: (groups) {
              if (groups.isEmpty) {
                return _buildGroupEmptyState();
              }

              return currentUserAsync.when(
                data: (currentUser) {
                  if (currentUser == null) {
                    return const Center(child: Text('User not found'));
                  }

                  return _buildGroupChatList(groups, currentUser);
                },
                loading: () => _buildLoadingState(),
                error: (error, stack) => Center(
                  child: Text('Error loading user: $error'),
                ),
              );
            },
            loading: () => _buildLoadingState(),
            error: (error, stack) => Center(
              child: Text('Error loading group chats: $error'),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          showModalBottomSheet(
            context: context,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
            ),
            builder: (context) {
              return SafeArea(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    ListTile(
                      leading: const Icon(Icons.person,
                          color: AppTheme.primaryColor),
                      title: const Text('New Direct Message'),
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const NewChatScreen(),
                          ),
                        );
                      },
                    ),
                    ListTile(
                      leading:
                          const Icon(Icons.group, color: AppTheme.primaryColor),
                      title: const Text('Create Group Chat'),
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const CreateGroupChatScreen(),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              );
            },
          );
        },
        backgroundColor: AppTheme.primaryColor,
        child: const Icon(Icons.chat, color: Colors.white),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    if (_isSearching) {
      return AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: _stopSearch,
        ),
        title: TextField(
          controller: _searchController,
          autofocus: true,
          decoration: InputDecoration(
            hintText: 'Search conversations...',
            hintStyle: TextStyle(
              color: Colors.white70,
              fontSize: 16,
            ),
            border: InputBorder.none,
          ),
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
          ),
          onChanged: _updateSearchQuery,
        ),
        backgroundColor: AppTheme.primaryColor,
      );
    } else {
      return CustomAppBar(
        title: 'Messages',
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _startSearch,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: AppTheme.primaryColor,
          labelColor: AppTheme.primaryColor,
          unselectedLabelColor: AppTheme.textSecondaryColor,
          tabs: const [
            Tab(text: 'Direct Messages'),
            Tab(text: 'Group Chats'),
          ],
        ),
      );
    }
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 80,
            color: Colors.grey[300],
          ),
          SizedBox(height: 16),
          Text(
            'No conversations yet',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Start chatting with guides and tourists',
            style: TextStyle(
              fontSize: 14,
              color: AppTheme.textSecondaryColor,
            ),
          ),
          SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              // Navigate to explore screen
            },
            icon: const Icon(Icons.explore),
            label: const Text('Explore Experiences'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGroupEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.group_outlined,
            size: 80,
            color: Colors.grey[300],
          ),
          SizedBox(height: 16),
          Text(
            'No group chats yet',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Create a group to chat with multiple people',
            style: TextStyle(
              fontSize: 14,
              color: AppTheme.textSecondaryColor,
            ),
          ),
          SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const CreateGroupChatScreen(),
                ),
              );
            },
            icon: const Icon(Icons.group_add),
            label: const Text('Create Group Chat'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChatList(List<ChatModel> chats) {
    // Filter chats based on search query
    final filteredChats = _searchQuery.isEmpty
        ? chats
        : chats.where((chat) {
            // We need to check if the chat contains the search query
            // This requires loading the participant data for each chat
            return false; // Placeholder, will be implemented with actual search
          }).toList();

    return ListView.builder(
      padding: EdgeInsets.symmetric(vertical: 8),
      itemCount: filteredChats.length,
      itemBuilder: (context, index) {
        final chat = filteredChats[index];
        return _buildChatTile(chat);
      },
    );
  }

  Widget _buildChatTile(ChatModel chat) {
    return Consumer(
      builder: (context, ref, child) {
        final participantsAsync = ref.watch(chatParticipantsProvider(chat.id));

        return participantsAsync.when(
          data: (participants) {
            if (participants.isEmpty) {
              return const SizedBox.shrink();
            }

            final recipient = participants.first;
            return ListTile(
              onTap: () => _navigateToChat(context, chat.id, recipient),
              leading: CircleAvatar(
                radius: 24,
                backgroundColor: Colors.grey[300],
                backgroundImage: recipient.profilePicture != null
                    ? NetworkImage(recipient.profilePicture!)
                    : null,
                child: recipient.profilePicture == null
                    ? Icon(
                        Icons.person,
                        color: Colors.white,
                        size: 24,
                      )
                    : null,
              ),
              title: Row(
                children: [
                  Expanded(
                    child: Text(
                      '${recipient.firstName} ${recipient.lastName}',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Text(
                    _formatTime(chat.lastMessageAt),
                    style: TextStyle(
                      fontSize: 12,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ],
              ),
              subtitle: Row(
                children: [
                  Expanded(
                    child: Text(
                      chat.lastMessageText.isEmpty
                          ? 'Start a conversation'
                          : chat.lastMessageText,
                      style: TextStyle(
                        fontSize: 14,
                        color: AppTheme.textSecondaryColor,
                        fontStyle: chat.lastMessageText.isEmpty
                            ? FontStyle.italic
                            : FontStyle.normal,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  if (chat.unreadCount > 0)
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        chat.unreadCount.toString(),
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),
              contentPadding: EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 8,
              ),
            );
          },
          loading: () => const SkeletonListTile(
            hasLeading: true,
            hasSubtitle: true,
            hasTrailing: true,
          ),
          error: (error, stack) => ListTile(
            leading: const CircleAvatar(
              child: Icon(Icons.error_outline),
            ),
            title: const Text('Error'),
            subtitle: Text('$error'),
          ),
        );
      },
    );
  }

  Widget _buildLoadingState() {
    return ListView.builder(
      padding: EdgeInsets.symmetric(vertical: 8),
      itemCount: 8,
      itemBuilder: (context, index) {
        return const SkeletonListTile(
          hasLeading: true,
          hasSubtitle: true,
          hasTrailing: true,
          leadingSize: 48.0,
        );
      },
    );
  }

  Widget _buildGroupChatList(
      List<GroupChatModel> groups, UserModel currentUser) {
    // Filter groups based on search query
    final filteredGroups = _searchQuery.isEmpty
        ? groups
        : groups.where((group) {
            final name = group.name.toLowerCase();
            final description = group.description.toLowerCase();
            return name.contains(_searchQuery) ||
                description.contains(_searchQuery);
          }).toList();

    return ListView.builder(
      padding: EdgeInsets.symmetric(vertical: 8),
      itemCount: filteredGroups.length,
      itemBuilder: (context, index) {
        final group = filteredGroups[index];
        return _buildGroupChatTile(group, currentUser);
      },
    );
  }

  Widget _buildGroupChatTile(GroupChatModel group, UserModel currentUser) {
    final unreadCount = group.getUnreadCount(currentUser.id);
    final role = group.getUserRole(currentUser.id);
    final roleText = role != null ? ' (${role.displayName})' : '';

    return ListTile(
      onTap: () => _navigateToGroupChat(context, group.id),
      leading: CircleAvatar(
        radius: 24,
        backgroundColor: Colors.grey[300],
        backgroundImage:
            group.imageUrl != null ? NetworkImage(group.imageUrl!) : null,
        child: group.imageUrl == null
            ? Icon(
                Icons.group,
                color: Colors.white,
                size: 24,
              )
            : null,
      ),
      title: Row(
        children: [
          Expanded(
            child: Text(
              '${group.name}$roleText',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Text(
            _formatTime(group.lastMessageAt),
            style: TextStyle(
              fontSize: 12,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
      subtitle: Row(
        children: [
          Expanded(
            child: Text(
              group.lastMessageText.isEmpty
                  ? 'No messages yet'
                  : '${group.lastMessageSenderId == currentUser.id ? 'You: ' : ''}${group.lastMessageText}',
              style: TextStyle(
                fontSize: 14,
                color: AppTheme.textSecondaryColor,
                fontStyle: group.lastMessageText.isEmpty
                    ? FontStyle.italic
                    : FontStyle.normal,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          if (unreadCount > 0)
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: 6,
                vertical: 2,
              ),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                unreadCount.toString(),
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
      contentPadding: EdgeInsets.symmetric(
        horizontal: 16,
        vertical: 8,
      ),
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = DateTime(now.year, now.month, now.day - 1);
    final messageDate = DateTime(
      dateTime.year,
      dateTime.month,
      dateTime.day,
    );

    if (messageDate == today) {
      return DateFormat('HH:mm').format(dateTime);
    } else if (messageDate == yesterday) {
      return 'Yesterday';
    } else if (now.difference(dateTime).inDays < 7) {
      return DateFormat('EEEE').format(dateTime); // Day of week
    } else {
      return DateFormat('dd/MM/yyyy').format(dateTime);
    }
  }
}
