import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:lottie/lottie.dart';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/models/payment/payment_api_models.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Payment success screen with celebration animations and mascot integration
class PaymentSuccessScreen extends StatefulWidget {
  final String transactionReference;
  final double amount;
  final PaymentMethodType paymentMethod;
  final Booking booking;
  final String? receiptId;

  const PaymentSuccessScreen({
    super.key,
    required this.transactionReference,
    required this.amount,
    required this.paymentMethod,
    required this.booking,
    this.receiptId,
  });

  @override
  State<PaymentSuccessScreen> createState() => _PaymentSuccessScreenState();
}

class _PaymentSuccessScreenState extends State<PaymentSuccessScreen>
    with TickerProviderStateMixin {
  late AnimationController _celebrationController;
  late AnimationController _slideController;
  late AnimationController _scaleController;

  @override
  void initState() {
    super.initState();

    _celebrationController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: AppTheme.mediumAnimation,
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: AppTheme.shortAnimation,
      vsync: this,
    );

    _startCelebration();
  }

  @override
  void dispose() {
    _celebrationController.dispose();
    _slideController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  /// Start celebration animations and haptic feedback
  void _startCelebration() async {
    // Trigger success haptic feedback
    HapticFeedback.heavyImpact();

    // Start animations
    _celebrationController.forward();

    await Future.delayed(const Duration(milliseconds: 500));
    if (mounted) {
      _slideController.forward();
    }

    await Future.delayed(const Duration(milliseconds: 200));
    if (mounted) {
      _scaleController.forward();
    }

    // Additional haptic feedback for celebration
    await Future.delayed(const Duration(milliseconds: 800));
    if (mounted) {
      HapticFeedback.lightImpact();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: SafeArea(
        child: Stack(
          children: [
            // Background celebration animation
            _buildBackgroundCelebration(),

            // Main content
            Column(
              children: [
                // Close button
                Align(
                  alignment: Alignment.topRight,
                  child: Padding(
                    padding: const EdgeInsets.all(AppTheme.spacingMedium),
                    child: IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => _navigateToBookingConfirmation(),
                    ),
                  ),
                ),

                // Success content
                Expanded(
                  child: _buildSuccessContent(theme),
                ),

                // Action buttons
                _buildActionButtons(theme),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build background celebration animation
  Widget _buildBackgroundCelebration() {
    return Positioned.fill(
      child: Lottie.asset(
        'assets/animations/celebration.json', // TODO: Add celebration animation
        controller: _celebrationController,
        fit: BoxFit.cover,
        repeat: false,
        onLoaded: (composition) {
          _celebrationController.duration = composition.duration;
        },
      ),
    );
  }

  /// Build main success content
  Widget _buildSuccessContent(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.all(AppTheme.spacingLarge),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Success icon with mascot
          _buildSuccessIcon(theme)
              .animate(controller: _scaleController)
              .scale(begin: const Offset(0.5, 0.5), end: const Offset(1.0, 1.0))
              .fadeIn(),

          const SizedBox(height: AppTheme.spacingLarge),

          // Success message
          Text(
            'Payment Successful!',
            style: theme.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.successColor,
            ),
            textAlign: TextAlign.center,
          )
              .animate(controller: _slideController)
              .slideY(begin: 0.3, end: 0)
              .fadeIn(delay: const Duration(milliseconds: 200)),

          const SizedBox(height: AppTheme.spacingSmall),

          Text(
            'Your booking has been confirmed!',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface.withAlpha(179),
            ),
            textAlign: TextAlign.center,
          )
              .animate(controller: _slideController)
              .slideY(begin: 0.3, end: 0)
              .fadeIn(delay: const Duration(milliseconds: 400)),

          const SizedBox(height: AppTheme.spacingLarge),

          // Payment details card
          _buildPaymentDetailsCard(theme)
              .animate(controller: _slideController)
              .slideY(begin: 0.3, end: 0)
              .fadeIn(delay: const Duration(milliseconds: 600)),

          const SizedBox(height: AppTheme.spacingMedium),

          // Achievement notification (if any)
          _buildAchievementNotification(theme)
              .animate(controller: _slideController)
              .slideY(begin: 0.3, end: 0)
              .fadeIn(delay: const Duration(milliseconds: 800)),
        ],
      ),
    );
  }

  /// Build success icon with mascot integration
  Widget _buildSuccessIcon(ThemeData theme) {
    return Stack(
      alignment: Alignment.center,
      children: [
        // Success circle background
        Container(
          width: 120,
          height: 120,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: AppTheme.successColor.withAlpha(26),
            border: Border.all(
              color: AppTheme.successColor.withAlpha(77),
              width: 2,
            ),
          ),
        ),

        // Success checkmark
        Container(
          width: 80,
          height: 80,
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
            color: AppTheme.successColor,
          ),
          child: const Icon(
            Icons.check,
            color: Colors.white,
            size: 48,
          ),
        ),

        // Mascot widget (celebrating expression)
        Positioned(
          bottom: -10,
          right: -10,
          child: Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: theme.colorScheme.surface,
              border: Border.all(
                color: theme.colorScheme.outline.withAlpha(51),
                width: 2,
              ),
            ),
            child: const Icon(
              Icons.celebration,
              size: 40,
              color: AppTheme.successColor,
            ),
          ),
        ),
      ],
    );
  }

  /// Build payment details card
  Widget _buildPaymentDetailsCard(ThemeData theme) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        side: BorderSide(
          color: theme.colorScheme.outline.withAlpha(51),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          children: [
            _buildDetailRow(
              theme,
              'Amount Paid',
              '\$${widget.amount.toStringAsFixed(2)}',
              isHighlighted: true,
            ),
            const SizedBox(height: AppTheme.spacingSmall),
            _buildDetailRow(
              theme,
              'Payment Method',
              _getPaymentMethodName(widget.paymentMethod),
            ),
            const SizedBox(height: AppTheme.spacingSmall),
            _buildDetailRow(
              theme,
              'Transaction ID',
              widget.transactionReference,
              isMonospace: true,
            ),
            const SizedBox(height: AppTheme.spacingSmall),
            _buildDetailRow(
              theme,
              'Date & Time',
              _formatDateTime(DateTime.now()),
            ),
          ],
        ),
      ),
    );
  }

  /// Build detail row
  Widget _buildDetailRow(
    ThemeData theme,
    String label,
    String value, {
    bool isHighlighted = false,
    bool isMonospace = false,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withAlpha(179),
          ),
        ),
        Text(
          value,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: isHighlighted ? FontWeight.w600 : FontWeight.w500,
            color: isHighlighted
                ? theme.colorScheme.primary
                : theme.colorScheme.onSurface,
            fontFamily: isMonospace ? 'monospace' : null,
          ),
        ),
      ],
    );
  }

  /// Build achievement notification
  Widget _buildAchievementNotification(ThemeData theme) {
    // TODO: Check if any achievements were unlocked
    // This would integrate with the AchievementService

    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer.withAlpha(51),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        border: Border.all(
          color: theme.colorScheme.primary.withAlpha(77),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.emoji_events,
            color: theme.colorScheme.primary,
            size: 24,
          ),
          const SizedBox(width: AppTheme.spacingSmall),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Achievement Unlocked!',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.primary,
                  ),
                ),
                Text(
                  'First Payment Completed',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withAlpha(179),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build action buttons
  Widget _buildActionButtons(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: theme.colorScheme.outline.withAlpha(51),
          ),
        ),
      ),
      child: Column(
        children: [
          // Primary action - View booking
          SizedBox(
            width: double.infinity,
            child: FilledButton(
              onPressed: _navigateToBookingConfirmation,
              child: const Text('View Booking Details'),
            ),
          ),

          const SizedBox(height: AppTheme.spacingSmall),

          // Secondary actions
          Row(
            children: [
              if (widget.receiptId != null) ...[
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _viewReceipt,
                    icon: const Icon(Icons.receipt, size: 18),
                    label: const Text('Receipt'),
                  ),
                ),
                const SizedBox(width: AppTheme.spacingSmall),
              ],
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _shareSuccess,
                  icon: const Icon(Icons.share, size: 18),
                  label: const Text('Share'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Navigate to booking confirmation
  void _navigateToBookingConfirmation() {
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(title: const Text('Booking Confirmed')),
          body: const Center(
            child: Text('Booking confirmation screen placeholder'),
          ),
        ),
      ),
      (route) => route.isFirst,
    );
  }

  /// View receipt
  void _viewReceipt() {
    if (widget.receiptId == null) return;

    // TODO: Navigate to receipt viewer
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Receipt viewer coming soon!'),
        backgroundColor: AppTheme.infoColor,
      ),
    );
  }

  /// Share success
  void _shareSuccess() {
    // TODO: Implement sharing functionality
    HapticFeedback.lightImpact();

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Sharing functionality coming soon!'),
        backgroundColor: AppTheme.infoColor,
      ),
    );
  }

  /// Get payment method display name
  String _getPaymentMethodName(PaymentMethodType method) {
    switch (method) {
      case PaymentMethodType.card:
        return 'Credit/Debit Card';
      case PaymentMethodType.bankTransfer:
        return 'Bank Transfer';
      case PaymentMethodType.ussd:
        return 'USSD';
      case PaymentMethodType.mobileMoney:
        return 'Mobile Money';
      case PaymentMethodType.crypto:
        return 'Cryptocurrency';
    }
  }

  /// Format date and time
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} at ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
