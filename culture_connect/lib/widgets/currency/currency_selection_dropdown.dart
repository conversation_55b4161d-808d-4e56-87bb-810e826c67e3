import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/currency/currency_model.dart';
import 'package:culture_connect/providers/currency/currency_providers.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A dropdown widget for selecting a currency
class CurrencySelectionDropdown extends ConsumerWidget {
  /// The currently selected currency code
  final String selectedCurrencyCode;

  /// Callback when a currency is selected
  final Function(String) onCurrencySelected;

  /// Whether to show the flag
  final bool showFlag;

  /// Whether to show the currency name
  final bool showName;

  /// Whether to show the currency code
  final bool showCode;

  /// Whether to show favorite currencies at the top
  final bool showFavorites;

  /// Whether to show recently used currencies at the top
  final bool showRecentlyUsed;

  /// Whether to show the search field
  final bool showSearch;

  /// The width of the dropdown
  final double? width;

  /// The height of the dropdown
  final double? height;

  /// Creates a new currency selection dropdown
  const CurrencySelectionDropdown({
    super.key,
    required this.selectedCurrencyCode,
    required this.onCurrencySelected,
    this.showFlag = true,
    this.showName = true,
    this.showCode = true,
    this.showFavorites = true,
    this.showRecentlyUsed = true,
    this.showSearch = true,
    thisidth,
    thiseight,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final allCurrencies = ref.watch(allCurrenciesProvider);
    final favoriteCurrencies = ref.watch(favoriteCurrenciesProvider);
    final recentlyUsedCurrencies = ref.watch(recentlyUsedCurrenciesProvider);

    // Find the selected currency
    final selectedCurrency = allCurrencies.firstWhere(
      (currency) => currency.code == selectedCurrencyCode,
      orElse: () => allCurrencies.first,
    );

    return GestureDetector(
      onTap: () {
        _showCurrencyPicker(
          context,
          ref,
          allCurrencies,
          favoriteCurrencies,
          recentlyUsedCurrencies,
        );
      },
      child: Container(
        width: width,
        height: height ?? 48,
        padding: EdgeInsets.symmetric(horizontal: 12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (showFlag) ...[
              Text(
                selectedCurrency.flag,
                style: TextStyle(fontSize: 16),
              ),
              SizedBox(width: 8),
            ],
            if (showCode) ...[
              Text(
                selectedCurrency.code,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(width: 8),
            ],
            if (showName) ...[
              Expanded(
                child: Text(
                  selectedCurrency.name,
                  style: TextStyle(fontSize: 14),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              SizedBox(width: 8),
            ],
            Icon(
              Icons.arrow_drop_down,
              color: Colors.grey[600],
            ),
          ],
        ),
      ),
    );
  }

  /// Show the currency picker dialog
  void _showCurrencyPicker(
    BuildContext context,
    WidgetRef ref,
    List<CurrencyModel> allCurrencies,
    List<CurrencyModel> favoriteCurrencies,
    List<CurrencyModel> recentlyUsedCurrencies,
  ) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(16),
        ),
      ),
      builder: (context) {
        return CurrencyPickerSheet(
          allCurrencies: allCurrencies,
          favoriteCurrencies: favoriteCurrencies,
          recentlyUsedCurrencies: recentlyUsedCurrencies,
          selectedCurrencyCode: selectedCurrencyCode,
          onCurrencySelected: (currencyCode) {
            Navigator.pop(context);
            onCurrencySelected(currencyCode);

            // Add to recently used currencies
            ref
                .read(currencyConversionServiceProvider)
                .addRecentlyUsedCurrency(currencyCode);
          },
          showFavorites: showFavorites,
          showRecentlyUsed: showRecentlyUsed,
          showSearch: showSearch,
        );
      },
    );
  }
}

/// A bottom sheet for picking a currency
class CurrencyPickerSheet extends StatefulWidget {
  /// All available currencies
  final List<CurrencyModel> allCurrencies;

  /// Favorite currencies
  final List<CurrencyModel> favoriteCurrencies;

  /// Recently used currencies
  final List<CurrencyModel> recentlyUsedCurrencies;

  /// The currently selected currency code
  final String selectedCurrencyCode;

  /// Callback when a currency is selected
  final Function(String) onCurrencySelected;

  /// Whether to show favorite currencies at the top
  final bool showFavorites;

  /// Whether to show recently used currencies at the top
  final bool showRecentlyUsed;

  /// Whether to show the search field
  final bool showSearch;

  /// Creates a new currency picker sheet
  const CurrencyPickerSheet({
    super.key,
    required this.allCurrencies,
    required this.favoriteCurrencies,
    required this.recentlyUsedCurrencies,
    required this.selectedCurrencyCode,
    required this.onCurrencySelected,
    required this.showFavorites,
    required this.showRecentlyUsed,
    required this.showSearch,
  });

  @override
  State<CurrencyPickerSheet> createState() => _CurrencyPickerSheetState();
}

class _CurrencyPickerSheetState extends State<CurrencyPickerSheet> {
  late TextEditingController _searchController;
  late List<CurrencyModel> _filteredCurrencies;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _filteredCurrencies = List.from(widget.allCurrencies);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// Filter currencies based on search query
  void _filterCurrencies(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredCurrencies = List.from(widget.allCurrencies);
      } else {
        _filteredCurrencies = widget.allCurrencies.where((currency) {
          return currency.code.toLowerCase().contains(query.toLowerCase()) ||
              currency.name.toLowerCase().contains(query.toLowerCase());
        }).toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Text(
                'Select Currency',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),

          // Search field
          if (widget.showSearch) ...[
            SizedBox(height: 16),
            TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search currencies',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              onChanged: _filterCurrencies,
            ),
          ],

          SizedBox(height: 16),

          // Currency lists
          Expanded(
            child: _searchController.text.isNotEmpty
                ? _buildCurrencyList(_filteredCurrencies, 'All Currencies')
                : SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Favorites
                        if (widget.showFavorites &&
                            widget.favoriteCurrencies.isNotEmpty)
                          _buildCurrencyList(
                              widget.favoriteCurrencies, 'Favorites'),

                        // Recently used
                        if (widget.showRecentlyUsed &&
                            widget.recentlyUsedCurrencies.isNotEmpty)
                          _buildCurrencyList(
                              widget.recentlyUsedCurrencies, 'Recently Used'),

                        // All currencies
                        _buildCurrencyList(
                            widget.allCurrencies, 'All Currencies'),
                      ],
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  /// Build a list of currencies with a header
  Widget _buildCurrencyList(List<CurrencyModel> currencies, String title) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.grey[600],
          ),
        ),
        SizedBox(height: 8),
        ...currencies.map((currency) => _buildCurrencyItem(currency)),
        SizedBox(height: 16),
      ],
    );
  }

  /// Build a currency item
  Widget _buildCurrencyItem(CurrencyModel currency) {
    final isSelected = currency.code == widget.selectedCurrencyCode;

    return InkWell(
      onTap: () => widget.onCurrencySelected(currency.code),
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.primaryColor.withAlpha(64) : null,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Text(
              currency.flag,
              style: TextStyle(fontSize: 20),
            ),
            SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    currency.name,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    currency.code,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: AppTheme.primaryColor,
                size: 20,
              ),
          ],
        ),
      ),
    );
  }
}
