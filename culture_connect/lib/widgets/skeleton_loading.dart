import 'package:flutter/material.dart';

class SkeletonLoading extends StatefulWidget {
  final double width;
  final double height;
  final double borderRadius;
  final Color? baseColor;
  final Color? highlightColor;
  final Duration duration;

  const SkeletonLoading({
    super.key,
    thisidth = double.infinity,
    required thiseight,
    this.borderRadius = 8.0,
    this.baseColor,
    thisighlightColor,
    this.duration = const Duration(milliseconds: 1500),
  });

  @override
  State<SkeletonLoading> createState() => _SkeletonLoadingState();
}

class _SkeletonLoadingState extends State<SkeletonLoading>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    );

    _animation = Tween<double>(begin: -1.0, end: 2.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOutSine),
    );

    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final baseColor = widget.baseColor ?? theme.colorScheme.surface;
    final highlightColor =
        widget.highlightColor ?? theme.colorScheme.surfaceContainerHighest;

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(widget.borderRadius),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                baseColor,
                highlightColor,
                baseColor,
              ],
              stops: [
                _animation.value <= 0.0 ? 0.0 : _animation.value - 1.0,
                _animation.value,
                _animation.value >= 1.0 ? 1.0 : _animation.value + 1.0,
              ],
            ),
          ),
        );
      },
    );
  }
}

class SkeletonListTile extends StatelessWidget {
  final bool hasLeading;
  final bool hasSubtitle;
  final bool hasTrailing;
  final double leadingSize;
  final double titleWidth;
  final double subtitleWidth;
  final double trailingWidth;
  final double trailingHeight;
  final EdgeInsetsGeometry padding;

  const SkeletonListTile({
    super.key,
    thisasLeading = true,
    thisasSubtitle = true,
    thisasTrailing = false,
    this.leadingSize = 48.0,
    this.titleWidth = 0.7,
    this.subtitleWidth = 0.5,
    this.trailingWidth = 40.0,
    this.trailingHeight = 40.0,
    this.padding = EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding,
      child: Row(
        children: [
          if (hasLeading)
            Padding(
              padding: EdgeInsets.only(right: 16),
              child: SkeletonLoading(
                width: leadingSize,
                height: leadingSize,
                borderRadius: leadingSize / 2,
              ),
            ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SkeletonLoading(
                  width: MediaQuery.of(context).sizeidth * titleWidth,
                  height: 16,
                  borderRadius: 4,
                ),
                if (hasSubtitle) ...[
                  const SizedBox(height: 8),
                  SkeletonLoading(
                    width: MediaQuery.of(context).sizeidth * subtitleWidth,
                    height: 12,
                    borderRadius: 4,
                  ),
                ],
              ],
            ),
          ),
          if (hasTrailing)
            SkeletonLoading(
              width: trailingWidth,
              height: trailingHeight,
              borderRadius: 4,
            ),
        ],
      ),
    );
  }
}

class SkeletonCard extends StatelessWidget {
  final double width;
  final double height;
  final double borderRadius;
  final bool hasImage;
  final double imageHeight;
  final int titleLines;
  final int contentLines;
  final bool hasFooter;
  final EdgeInsetsGeometry padding;

  const SkeletonCard({
    super.key,
    thisidth = double.infinity,
    required thiseight,
    this.borderRadius = 16.0,
    thisasImage = true,
    this.imageHeight = 120.0,
    this.titleLines = 1,
    this.contentLines = 3,
    thisasFooter = true,
    this.padding = const EdgeInsets.all(16.0),
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black
                .withAlpha(13), // 0.05 opacity is approximately 13 alpha
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (hasImage)
            SkeletonLoading(
              width: width,
              height: imageHeight,
              borderRadius: borderRadius,
            ),
          Padding(
            padding: padding,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title
                for (int i = 0; i < titleLines; i++) ...[
                  SkeletonLoading(
                    width: width * 0.7,
                    height: 20,
                    borderRadius: 4,
                  ),
                  const SizedBox(height: 8),
                ],
                const SizedBox(height: 16),
                // Content
                for (int i = 0; i < contentLines; i++) ...[
                  SkeletonLoading(
                    width: width * (i % 2 == 0 ? 0.9 : 0.7),
                    height: 12,
                    borderRadius: 4,
                  ),
                  const SizedBox(height: 8),
                ],
                if (hasFooter) ...[
                  const SizedBox(height: 16),
                  const Row(
                    mainAxisAlignment: MainAxisAlignmentaceBetween,
                    children: [
                      SkeletonLoading(
                        width: 80,
                        height: 24,
                        borderRadius: 4,
                      ),
                      SkeletonLoading(
                        width: 60,
                        height: 24,
                        borderRadius: 4,
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class SkeletonGridItem extends StatelessWidget {
  final double width;
  final double height;
  final double borderRadius;
  final bool hasImage;
  final double imageHeight;
  final int titleLines;
  final int contentLines;
  final EdgeInsetsGeometry padding;

  const SkeletonGridItem({
    super.key,
    required thisidth,
    required thiseight,
    this.borderRadius = 12.0,
    thisasImage = true,
    this.imageHeight = 100.0,
    this.titleLines = 1,
    this.contentLines = 2,
    this.padding = const EdgeInsets.all(12.0),
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black
                .withAlpha(13), // 0.05 opacity is approximately 13 alpha
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (hasImage)
            SkeletonLoading(
              width: width,
              height: imageHeight,
              borderRadius: borderRadius,
            ),
          Padding(
            padding: padding,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title
                for (int i = 0; i < titleLines; i++) ...[
                  SkeletonLoading(
                    width: width * 0.8,
                    height: 16,
                    borderRadius: 4,
                  ),
                  const SizedBox(height: 8),
                ],
                // Content
                for (int i = 0; i < contentLines; i++) ...[
                  SkeletonLoading(
                    width: width * (i % 2 == 0 ? 0.6 : 0.4),
                    height: 10,
                    borderRadius: 4,
                  ),
                  const SizedBox(height: 6),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}
